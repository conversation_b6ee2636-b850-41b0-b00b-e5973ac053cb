# syntax=docker/dockerfile:1

# Base stage for shared dependencies
FROM node:18-alpine AS base
WORKDIR /app
ENV NODE_ENV=production

# Install system dependencies
RUN apk add --no-cache \
  wget \
  curl \
  && rm -rf /var/cache/apk/*

# Development stage
FROM base AS development
ENV NODE_ENV=development
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000 8080
CMD ["npm", "run", "dev"]

# Build stage
FROM base AS builder
COPY package*.json ./
RUN npm ci --include=dev
COPY . .
RUN npm run build

# Production stage
FROM base AS production
COPY package*.json ./
RUN npm ci --production && npm cache clean --force

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/public ./public

# Create data directories with proper permissions
RUN mkdir -p /app/data /app/logs /app/uploads /app/temp && \
  chmod -R 755 /app/data /app/logs /app/uploads /app/temp && \
  chown -R node:node /app

# Switch to non-root user
USER node

# Expose ports for HTTP server and WebSocket
EXPOSE 3000 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1

# Start the server
CMD ["npm", "start"]
