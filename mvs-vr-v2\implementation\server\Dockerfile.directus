FROM directus/directus:10.8

# Install additional dependencies if needed
RUN npm install --no-save \
  directus-extension-computed-interface \
  directus-extension-group-tabs-interface

# Copy custom extensions if any
# COPY ./extensions /directus/extensions

# Set environment variables
ENV KEY=directus-secret-key
ENV SECRET=directus-secret
ENV ADMIN_EMAIL=<EMAIL>
ENV ADMIN_PASSWORD=admin
ENV DB_CLIENT=pg
ENV DB_HOST=supabase-db
ENV DB_PORT=5432
ENV DB_DATABASE=postgres
ENV DB_USER=postgres
ENV DB_PASSWORD=postgres

# Expose port
EXPOSE 8055

# Health check
HEALTHCHECK --interval=10s --timeout=5s --start-period=30s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8055/server/health || exit 1
