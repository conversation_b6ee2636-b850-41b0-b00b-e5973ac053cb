# Build Docker script

param (
    [switch]$SkipValidation,
    [switch]$ForceRebuild
)

$ErrorActionPreference = "Stop"

# Validate Docker setup if not skipped
if (-not $SkipValidation) {
    Write-Host "Validating Docker setup..." -ForegroundColor Cyan
    & ".\scripts\validate-docker.ps1"
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Docker validation failed. Please fix the issues before continuing." -ForegroundColor Red
        exit 1
    }
}

# Stop any running containers
Write-Host "Stopping any running containers..." -ForegroundColor Yellow
docker-compose down
if ($LASTEXITCODE -ne 0) {
    Write-Host "Warning: Failed to stop containers. Continuing anyway..." -ForegroundColor Yellow
}

# Remove dangling images if force rebuild
if ($ForceRebuild) {
    Write-Host "Removing dangling images..." -ForegroundColor Yellow
    docker image prune -f
}

# Build the containers
Write-Host "Building containers (this may take some time)..." -ForegroundColor Yellow
$buildArgs = @()
if ($ForceRebuild) {
    $buildArgs += "--no-cache"
}
docker-compose build $buildArgs
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to build containers." -ForegroundColor Red
    exit 1
}

Write-Host "Docker build completed successfully!" -ForegroundColor Green
Write-Host "You can now run 'docker-compose up -d' to start the containers." -ForegroundColor Cyan

exit 0